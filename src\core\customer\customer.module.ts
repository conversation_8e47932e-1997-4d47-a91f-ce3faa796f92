import { Module } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CustomerController } from './customer.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Customer } from '@core/customer/entities/customer.entity';
import { LoggerModule } from '@common/logger/logger.module';
import { CustomerMapperService } from './customer.mapper.service';
import { CustomerValidationService } from './customer.validation.service';

@Module({
  controllers: [CustomerController],
  providers: [CustomerService, CustomerMapperService, CustomerValidationService],
  exports: [CustomerService],
  imports: [
    TypeOrmModule.forFeature([Customer]),
    LoggerModule
  ],
})
export class CustomerModule {}
