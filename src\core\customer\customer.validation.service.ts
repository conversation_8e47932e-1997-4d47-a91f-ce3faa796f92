import { Injectable, NotFoundException } from '@nestjs/common';
import { Customer } from './entities/customer.entity';
import { ValidationStrategy } from '@common/pattern/validation.strategy';
import { LoggerService } from '@common/logger/logger.service';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { Repository } from 'typeorm';

@Injectable()
export class CustomerValidationService implements ValidationStrategy<Customer>  {
 constructor(
    private readonly logger: LoggerService,
    @InjectRepository(Customer) private readonly staffRepository: Repository<Customer>,
    private readonly i18n: I18nService,
  ) {
    this.logger.setContext(CustomerValidationService.name);
  }
  async validate(data: Customer, action: DatabaseAction) {
    const existingCustomer = await this.staffRepository.findOneBy({ id: data.id });

    if (action === DatabaseAction.CREATE) {
    }

    if (action === DatabaseAction.UPDATE && !existingCustomer) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Customer' } }));
    }
  }
}
