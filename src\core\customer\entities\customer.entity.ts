import { AutoMap } from '@automapper/classes';
import { AbstractEntity, EntityStatus } from '@common/entities/base.entity';
import { Order } from '@core/order/entities/order.entity';
import { Profile } from '@core/profile/entities/profile.entity';
import { Review } from '@core/review/entities/review.entity';
import { Wallet } from '@core/wallet/entities/wallet.entity';
import { BeforeInsert, Column, Entity, JoinColumn, OneToMany, OneToOne } from 'typeorm';

/**
 * Customer entity representing a customer in the system.
 * It contains a reference to the customer's profile, wallet, and their orders, reviews, and carts.
 */
@Entity({ name: 'customer' })
export class Customer extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'referrer_code', nullable: true, type: 'varchar', length: 100 })
  referrerCode: string;

  @AutoMap(() => Profile)
  @OneToOne(() => Profile, { eager: true })
  @JoinColumn({ name: 'profile_id', referencedColumnName: 'id' })
  profile: Profile;

  @AutoMap(() => Wallet)
  @OneToOne(() => Wallet, { eager: true })
  @JoinColumn({ name: 'wallet_id', referencedColumnName: 'id' })
  wallet: Wallet;

  // TODO: Add as project progresses
  // @AutoMap(() => Review)
  // @OneToMany(() => Review, (review) => review.customer, { eager: true })
  // reviews: Review[]; // Assuming this is a one-to-many relationship with Review

  // @AutoMap(() => Order)
  // @OneToMany(() => Order, (order) => order.customer)
  // orders: Order[];
}
