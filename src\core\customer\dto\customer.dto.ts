import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { ProfileDto } from '@core/profile/dto/profile.dto';
import { Profile } from '@core/profile/entities/profile.entity';
import { WalletDto } from '@core/wallet/dto/wallet.dto';
import { Wallet } from '@core/wallet/entities/wallet.entity';

export class CustomerDto extends EntityDto {
  @AutoMap()
  referrerCode: string;

  @AutoMap(() => ProfileDto)
  profile: ProfileDto;

  @AutoMap(() => WalletDto)
  wallet: WalletDto;
}
