import { AbstractEntity } from '@common/entities/base.entity';
import { Column, <PERSON>tity, JoinColumn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { OrderItem } from '@core/order_item/entities/order_item.entity';
import { AutoMap } from '@automapper/classes';
import { OrderStatus } from '@common/enumerations/order_status.enum';
import { Customer } from '@core/customer/entities/customer.entity';


/**
 * Order entity representing an order in the system.
 * It contains information about the order date, fee, status, customer, recipient details, and associated order items.
 */
@Entity({ name: 'order' })
export class Order extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'order_date', type: 'timestamp' })
  orderDate: Date;

  @AutoMap()
  @Column({ name: 'order_fee', type: 'bigint' })
  orderFee: number;

  @AutoMap(() => String)
  @Column({
    name: 'orderStatus',
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  orderStatus: OrderStatus;

  // TODO: Add as project progresses
  // @AutoMap(() => Customer)
  // @ManyToOne(() => Customer, (customer) => customer.orders)
  // @JoinColumn({ name: 'customer_id', referencedColumnName: 'id' })
  // customer: Customer;

  @AutoMap()
  @Column({ name: 'recipient_name', type: 'varchar' })
  recipientName: string;

  @AutoMap()
  @Column({ name: 'recipient_phone_number', type: 'varchar' })
  recipientPhoneNumber: string;

  @AutoMap()
  @Column({ name: 'recipient_email', type: 'varchar' })
  recipientEmail: string;

  @AutoMap(() => OrderItem)
  @OneToMany(() => OrderItem, (item) => item.order, {
    cascade: true,
    eager: true,
    orphanedRowAction: 'delete',
  })
  items: OrderItem[];

  @AutoMap()
  @Column({ name: 'total_amount', type: 'bigint' })
  totalAmount: number;
}
