import { AutoMap } from '@automapper/classes';
import { BeforeI<PERSON>rt, <PERSON>tity, <PERSON>inC<PERSON>umn, OneToOne } from 'typeorm';
import { AbstractEntity, EntityStatus } from '@common/entities/base.entity';
import { Profile } from '@core/profile/entities/profile.entity';

/**
 * Staff entity - Represents a staff member in the system.
 * Extends AbstractEntity which includes common entity fields.
 */
@Entity({ name: 'staff' })
export class Staff extends AbstractEntity {
  @AutoMap(() => Profile)
  @OneToOne(() => Profile, { cascade: true, eager: true })
  @JoinColumn({ name: 'profile_id', referencedColumnName: 'id' })
  profile: Profile;
}
