import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { Controller, Get, Param } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { I18nService } from 'nestjs-i18n';
import { StaffDto } from './dto/staff.dto';
import { Staff } from './entities/staff.entity';
import { StaffService } from './staff.service';

@ApiTags('Staff')
@Controller({
  path: 'staff',
  version: '1',
})
export class StaffController {
  constructor(
    private readonly staffService: StaffService,
    private readonly logger: LoggerService,
    private readonly i18n: I18nService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(StaffController.name);
  }
  @ApiOperation({ summary: 'Get all staff' })
  @Get()
  async getAllStaff(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const staff = await this.staffService.findAllStaff();
      const data = await this.classMapper.mapArrayAsync(staff, Staff, StaffDto);
      return { message: this.i18n.t('message.success.retrieved', { args: { entity: 'Staff' } }), data };
    });
  }

  @ApiOperation({ summary: 'Get one staff' })
  @Get(':id')
  async getOneStaff(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const staff = await this.staffService.findOneStaff(id);
      const data = await this.classMapper.mapAsync(staff, Staff, StaffDto);
      return { message: this.i18n.t('message.success.retrieved', { args: { entity: 'Staff' } }), data };
    });
  }
  //
  // @Public()
  // @ApiOkResponse({ description: 'Staff updated successfully' })
  // @ApiNotFoundResponse()
  // @Patch(':id')
  // async update(
  //   @Param('id') id: string,
  //   @Body() updateStaffDto: UpdateStaffDto,
  // ): Promise<Staff | object> {
  //   return this.staffService.update(+id, updateStaffDto);
  // }
  //
  // @Public()
  // @ApiOkResponse({ description: 'Staff deleted successfully' })
  // @ApiNotFoundResponse()
  // @Delete(':id')
  // async remove(@Param('id') id: string): Promise<Staff | object> {
  //   return this.staffService.remove(+id);
  // }
}
